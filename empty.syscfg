/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const UART   = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1  = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const gate4  = system.clockTree["EXCLKGATE"];
gate4.enable = true;

const mux3       = system.clockTree["EXCLKMUX"];
mux3.inputSelect = "EXCLKMUX_HFCLK";

const mux6       = system.clockTree["FCCSELCLKMUX"];
mux6.inputSelect = "FCCSELCLKMUX_HFCLK";

const pinFunction3     = system.clockTree["HFCLKEXT"];
pinFunction3.enable    = true;
pinFunction3.inputFreq = 40;

SYSCTL.forceDefaultClkConfig = true;

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

UART1.$name             = "UART_0";
UART1.targetBaudRate    = 115200;
UART1.txPinConfig.$name = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name = "ti_driverlib_gpio_GPIOPinGeneric1";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction3.peripheral.$suggestSolution            = "SYSCTL";
pinFunction3.peripheral.hfclkInPin.$suggestSolution = "PA6";
SYSCTL.peripheral.$suggestSolution                  = "SYSCTL";
Board.peripheral.$suggestSolution                   = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution          = "PA20";
Board.peripheral.swdioPin.$suggestSolution          = "PA19";
UART1.peripheral.$suggestSolution                   = "UART0";
UART1.peripheral.rxPin.$suggestSolution             = "PA1";
UART1.peripheral.txPin.$suggestSolution             = "PA0";
