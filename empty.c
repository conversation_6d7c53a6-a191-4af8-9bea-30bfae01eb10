/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "ti_msp_dl_config.h"
#include "retarget.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

/* 常量定义 */
#define WAVE_OUTPUT_POINTS  1024    /* 波形数据点数 */
#define ADDT_CHUNK_SIZE     128     /* 每次发送的数据块大小 */

/* 全局变量 - 模拟数据 */
static float gLastFundFreq = 50.0f;     /* 基频 Hz */
static float avgPP[5];                  /* 基波~五次峰峰值 */
static float avgTHD = 0.05f;            /* THD% (0~1 范围) */
static uint8_t gWaveBuf[WAVE_OUTPUT_POINTS]; /* 波形数据缓冲区 */

/* 简单的延时函数 (基于 CPU 循环) */
static void delay_ms(uint32_t ms)
{
    /* 假设 32MHz 时钟，粗略估算延时循环 */
    /* 实际应用中建议使用定时器实现精确延时 */
    volatile uint32_t cycles = ms * 8000; /* 粗略估算 */
    while (cycles--) {
        __asm("nop");
    }
}

/* 发送命令结束符 */
static void sendCmdEnd(void)
{
    uart_send_raw(0xFF);
    uart_send_raw(0xFF);
    uart_send_raw(0xFF);
}

/* 生成模拟数据 */
static void generateSimulatedData(void)
{
    static uint32_t counter = 0;
    counter++;

    /* 基频变化 (45-55Hz) */
    gLastFundFreq = 45.0f + (counter % 100) * 0.1f;

    /* 基波~五次峰峰值 (模拟变化的数据) */
    avgPP[0] = 3.3f + 0.1f * sinf(counter * 0.1f);      /* 基波 */
    avgPP[1] = 0.5f + 0.05f * sinf(counter * 0.15f);    /* 二次 */
    avgPP[2] = 0.3f + 0.03f * sinf(counter * 0.2f);     /* 三次 */
    avgPP[3] = 0.2f + 0.02f * sinf(counter * 0.25f);    /* 四次 */
    avgPP[4] = 0.1f + 0.01f * sinf(counter * 0.3f);     /* 五次 */

    /* THD% 变化 (3-8%) */
    avgTHD = 0.03f + 0.05f * (sinf(counter * 0.05f) + 1.0f) / 2.0f;

    /* 生成模拟波形数据 (正弦波 + 谐波) */
    for (uint16_t i = 0; i < WAVE_OUTPUT_POINTS; i++) {
        float angle = 2.0f * M_PI * i / WAVE_OUTPUT_POINTS;
        float wave = 127.5f + 100.0f * sinf(angle) +
                     10.0f * sinf(3.0f * angle) +
                     5.0f * sinf(5.0f * angle);

        /* 限制在 0-255 范围内 */
        if (wave < 0) wave = 0;
        if (wave > 255) wave = 255;

        gWaveBuf[i] = (uint8_t)wave;
    }
}

int main(void)
{
    SYSCFG_DL_init();

    /* 初始化模拟数据 */
    generateSimulatedData();

    while (1) {
        /* 生成新的模拟数据 */
        generateSimulatedData();

        /* x0: 基频 (整数 Hz) */
        printf("x0.val=%u", (uint32_t)(gLastFundFreq + 0.5f));
        sendCmdEnd();

        /* x1..x5: 基波~五次峰峰值 (4 位小数，乘10000 转 int) */
        uint32_t val;
        val = (uint32_t)(avgPP[0] * 10000.0f + 0.5f);
        printf("x1.val=%u", val); sendCmdEnd();
        val = (uint32_t)(avgPP[1] * 10000.0f + 0.5f);
        printf("x2.val=%u", val); sendCmdEnd();
        val = (uint32_t)(avgPP[2] * 10000.0f + 0.5f);
        printf("x3.val=%u", val); sendCmdEnd();
        val = (uint32_t)(avgPP[3] * 10000.0f + 0.5f);
        printf("x4.val=%u", val); sendCmdEnd();
        val = (uint32_t)(avgPP[4] * 10000.0f + 0.5f);
        printf("x5.val=%u", val); sendCmdEnd();

        /* x6: THD% (四位小数，乘10000) */
        val = (uint32_t)(avgTHD * 10000.0f + 0.5f); /* avgTHD 已为 0~1 范围 */
        printf("x6.val=%u", val); sendCmdEnd();
        delay_ms(100);

        /* --------- 发送波形数据 --------- */
        for (uint16_t offset = 0; offset < WAVE_OUTPUT_POINTS; offset += ADDT_CHUNK_SIZE) {
            /* 1. 发送 addt 指令, obj id = 15, ch = 0, qty = 128 */
            printf("addt 15,0,128");
            uart_send_raw(0xFF);
            uart_send_raw(0xFF);
            uart_send_raw(0xFF);

            /* 给 Nextion 一点时间进入透传缓冲；若后续要更稳健可读取 0x05 00 01 FF FF FF 应答 */
            delay_ms(50);

            /* 2. 发送 128 字节数据 */
            for (uint16_t i = 0; i < ADDT_CHUNK_SIZE; i++) {
                uart_send_raw(gWaveBuf[offset + i]);
            }

            /* 3. 结束帧 */
            uart_send_raw(0x01);
            uart_send_raw(0xFF);
            uart_send_raw(0xFF);
            uart_send_raw(0xFF);

            /* 间隔确保 Nextion 消化数据，再进下一块 */
            delay_ms(50);
        }

        /* 等待约1秒再发送下一组数据 */
        delay_ms(1000);
    }
}
