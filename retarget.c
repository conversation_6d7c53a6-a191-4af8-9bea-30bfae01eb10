// retarget.c - Redirects standard I/O (e.g. printf) to UART0
#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <ctype.h>
#include "ti_msp_dl_config.h"

/*
 * This implementation sends characters generated by the C standard library
 * (e.g. printf) out through the UART instance configured in SysConfig.
 *
 * It implements both the low-level `write` syscall used by TI's rts library
 * **and** the higher-level `fputc` fallback that some toolchains call.
 * Either route ultimately transmits each byte with `DL_UART_transmitDataBlocking`.
 */

/*
 * The TX FIFO on MSPM0 is only one entry deep when FIFO mode is disabled.
 * If characters are written back-to-back faster than the hardware can shift
 * them out the newest byte silently overwrites the previous one – the user
 * then observes that *every other* character is missing on the terminal.
 *
 * Waiting for "not busy" ensures that both the FIFO *and* the shift register
 * are empty before we push the next byte, which makes the routine 100 % safe   
 * at the cost of a few CPU cycles (still < 1 µs at 32 MHz / 115200 Bd).
 */
static inline void _uart_send_byte(uint8_t byte)
{
    /* Use blocking transmission to ensure each byte is completely sent
     * before the next one starts. This prevents any possibility of
     * character corruption or reordering.
     */
    DL_UART_transmitDataBlocking(UART_0_INST, byte);
}

/* Ensure \n is sent as CR+LF so most terminals interpret newline correctly */
static void _uart_send_char(int ch)
{
    if (ch == '\n') {
        /* Send LF first to advance to the next line, then CR to return the
         * cursor to column 0 of that new line.  This prevents overwriting of
         * the beginning of the line when two printf calls occur back-to-back
         * at high speed (the exact symptom you observed: "1230123456789").
         */
        _uart_send_byte('\r'); /* Line Feed – go to next line                */
        _uart_send_byte('\n'); /* Carriage Return – back to column 0          */
        return;
    }

    _uart_send_byte((uint8_t)ch);
}

/*
 * TI Arm Clang RTS uses `write` for stdout/stderr.  Re-implement it here to
 * funnel all buffered stdio output to UART0.
 */
int write(int handle, const unsigned char *buffer, unsigned int count)
{
    static volatile int uart_busy = 0;
    
    /* Only handle stdout (1) and stderr (2). Return error for others */
    if (handle != 1 && handle != 2) {
        return -1;
    }

    /* Simple mutex to prevent printf calls from interfering */
    while (uart_busy) {
        /* wait for previous write to complete */
    }
    uart_busy = 1;

    for (unsigned int i = 0; i < count; i++) {
        _uart_send_char(buffer[i]);
    }
    
    uart_busy = 0;
    return count;
}

/*
 * Some library variants call `fputc`/`putchar` directly instead of `write`.
 * Provide them as thin wrappers so they behave consistently.
 */
int fputc(int ch, FILE *stream)
{
    (void)stream; /* Unused */
    _uart_send_char(ch);
    return ch;
}

int putchar(int ch)
{
    _uart_send_char(ch);
    return ch;
}

/* =====================================================================
 * Minimal printf implementation – supports %d, %u, %f and %% only
 * =====================================================================
 * Implemented here instead of pulling in the full-size printf from the
 * C run-time in order to keep the firmware image small while still
 * giving the developer convenient formatted output over UART.
 *
 * Limitations:
 *   1. Field width, padding, long/short modifiers, etc. are not handled.
 *   2. For %f, the precision defaults to PRINTF_FLOAT_PRECISION decimal places (compile-time configurable).
 *   3. Floats are handled as doubles because of default argument promotion.
 *
 * If you need more features, consider replacing this with a more capable
 * implementation such as "tinyprintf" or newlib-nano.
 */
#include <stdarg.h>

#ifndef PRINTF_FLOAT_PRECISION
#define PRINTF_FLOAT_PRECISION 7 /* 默认保留的小数位数，可在编译选项中 -DPRINTF_FLOAT_PRECISION=N 覆盖 */
#endif

static void _uart_send_string(const char *s)
{
    while (*s) {
        _uart_send_char((uint8_t)*s++);
    }
}

/* Converts a signed integer to string. Returns pointer to buffer start. */
static char *_itoa(int value, char *buf_end)
{
    /* Reserve space for null terminator */
    *--buf_end = '\0';

    bool negative = false;
    if (value < 0) {
        negative = true;
        value = -value;
    }

    /* Generate digits in reverse order */
    do {
        *--buf_end = (char)('0' + (value % 10));
        value /= 10;
    } while (value);

    if (negative) {
        *--buf_end = '-';
    }

    return buf_end;
}

/* Converts an unsigned integer to string. Returns pointer to buffer start. */
static char *_utoa(unsigned int value, char *buf_end)
{
    *--buf_end = '\0';

    do {
        *--buf_end = (char)('0' + (value % 10));
        value /= 10;
    } while (value);

    return buf_end;
}

/* simple power-of-10 helper (max precision 9 keeps within 32-bit) */
static unsigned long long _pow10_u64(int n)
{
    unsigned long long p = 1;
    while (n--) p *= 10ULL;
    return p;
}

/* Converts a floating point value to string with rounding and zero-padding. */
static void _ftoa(double value, char *buf, int precision)
{
    if (precision <= 0) precision = 0;
    if (precision > 9)  precision = 9; /* limit to keep computations within 32-bit */

    bool negative = false;
    if (value < 0) {
        negative = true;
        value = -value;
    }

    unsigned int scale = (unsigned int)_pow10_u64(precision);

    /* rounding: add 0.5 at the (precision+1)th digit */
    double scaled_d = value * (double)scale + 0.5;
    unsigned long long scaled = (unsigned long long)scaled_d;

    unsigned int int_part = (unsigned int)(scaled / scale);
    unsigned int frac_part = (unsigned int)(scaled % scale);

    /* write sign */
    if (negative) {
        *buf++ = '-';
    }

    /* integer part */
    char tmp_int[16];
    char *p_int = _utoa(int_part, tmp_int + sizeof(tmp_int));
    while (*p_int) {
        *buf++ = *p_int++;
    }

    *buf++ = '.';

    /* fractional part with zero-padding */
    char tmp_frac[11]; /* up to 10 digits + null */
    char *p_frac = _utoa(frac_part, tmp_frac + sizeof(tmp_frac));
    int digits_frac = (int)((tmp_frac + sizeof(tmp_frac) - 1) - p_frac);
    for (int i = 0; i < precision - digits_frac; i++) {
        *buf++ = '0';
    }
    while (*p_frac) {
        *buf++ = *p_frac++;
    }

    *buf = '\0';
}

/* Converts an unsigned integer to hexadecimal string with padding. */
static char *_utohex_padded(unsigned long value, char *buf_end, bool uppercase, int width)
{
    *--buf_end = '\0';
    
    const char *digits = uppercase ? "0123456789ABCDEF" : "0123456789abcdef";
    int digit_count = 0;
    
    do {
        *--buf_end = digits[value & 0xF];
        value >>= 4;
        digit_count++;
    } while (value);
    
    /* Add leading zeros if width is specified */
    while (digit_count < width) {
        *--buf_end = '0';
        digit_count++;
    }
    
    return buf_end;
}

/* Converts a long integer to string. Returns pointer to buffer start. */
static char *_ltoa(long value, char *buf_end)
{
    *--buf_end = '\0';

    bool negative = false;
    if (value < 0) {
        negative = true;
        value = -value;
    }

    do {
        *--buf_end = (char)('0' + (value % 10));
        value /= 10;
    } while (value);

    if (negative) {
        *--buf_end = '-';
    }

    return buf_end;
}

/* Converts an unsigned long to string. Returns pointer to buffer start. */
static char *_ultoa(unsigned long value, char *buf_end)
{
    *--buf_end = '\0';

    do {
        *--buf_end = (char)('0' + (value % 10));
        value /= 10;
    } while (value);

    return buf_end;
}

/* Minimal printf */
int printf(const char *fmt, ...)
{
    va_list args;
    va_start(args, fmt);
    int count = 0;

    while (*fmt) {
        if (*fmt != '%') {
            _uart_send_char((uint8_t)*fmt++);
            count++;
            continue;
        }

        fmt++; /* Skip '%' */

        /* === Format parsing logic (proper order) === */
        bool zero_pad = false;
        int width = 0;
        int precision = -1;
        bool is_long = false;
        
        /* 1. Check for flags (zero padding) */
        if (*fmt == '0') {
            zero_pad = true;
            fmt++;
        }

        /* 2. Parse width */
        while (isdigit((unsigned char)*fmt)) {
            width = width * 10 + (*fmt - '0');
            fmt++;
        }

        /* 3. Parse precision */
        if (*fmt == '.') {
            fmt++;
            precision = 0;
            while (isdigit((unsigned char)*fmt)) {
                precision = precision * 10 + (*fmt - '0');
                fmt++;
            }
        }

        /* 4. Parse length modifiers */
        if (*fmt == 'l') {
            is_long = true;
            fmt++;
        }

        /* 5. Parse format specifier */
        char spec = *fmt;
        switch (spec) {
        case '%':
            _uart_send_char('%');
            count++;
            break;
            
        case 'd': {
            if (is_long) {
                long val = va_arg(args, long);
                char tmp[24];
                char *str = _ltoa(val, tmp + sizeof(tmp));
                _uart_send_string(str);
                const char *s = str; while (*s++) count++;
            } else {
                int val = va_arg(args, int);
                char tmp[16];
                char *str = _itoa(val, tmp + sizeof(tmp));
                _uart_send_string(str);
                const char *s = str; while (*s++) count++;
            }
            break;
        }
        
        case 'u': {
            if (is_long) {
                unsigned long val = va_arg(args, unsigned long);
                char tmp[24];
                char *str = _ultoa(val, tmp + sizeof(tmp));
                _uart_send_string(str);
                const char *s = str; while (*s++) count++;
            } else {
                unsigned int val = va_arg(args, unsigned int);
                char tmp[16];
                char *str = _utoa(val, tmp + sizeof(tmp));
                _uart_send_string(str);
                const char *s = str; while (*s++) count++;
            }
            break;
        }
        
        case 'X':
        case 'x': {
            bool uppercase = (spec == 'X');
            if (is_long) {
                unsigned long val = va_arg(args, unsigned long);
                char tmp[20];
                char *str = _utohex_padded(val, tmp + sizeof(tmp), uppercase, width);
                _uart_send_string(str);
                const char *s = str; while (*s++) count++;
            } else {
                unsigned int val = va_arg(args, unsigned int);
                char tmp[12];
                char *str = _utohex_padded(val, tmp + sizeof(tmp), uppercase, width);
                _uart_send_string(str);
                const char *s = str; while (*s++) count++;
            }
            break;
        }
        
        case 's': {
            const char *str = va_arg(args, const char *);
            if (str == NULL) {
                str = "(null)";
            }
            _uart_send_string(str);
            const char *s = str; while (*s++) count++;
            break;
        }
        
        case 'c': {
            int ch = va_arg(args, int);
            _uart_send_char((uint8_t)ch);
            count++;
            break;
        }
        
        case 'f': {
            double val = va_arg(args, double);
            int prec = (precision < 0) ? PRINTF_FLOAT_PRECISION : precision;
            char buf[64];
            _ftoa(val, buf, prec);
            _uart_send_string(buf);
            const char *s = buf; while (*s++) count++;
            break;
        }
        
        default:
            /* Unknown format specifier - just print it literally */
            _uart_send_char('%');
            _uart_send_char((uint8_t)spec);
            count += 2;
            break;
        }

        fmt++; /* move past format specifier */
    }

    va_end(args);
    return count;
} 

/* === Raw transmit helper: send one byte exactly as given (no CR/LF translation) === */
void uart_send_raw(uint8_t byte)
{
    /* Directly invoke the same low-level helper so timing is identical. */
    _uart_send_byte(byte);
}

/* === Function declarations for external use === */
/* 这些函数声明可以被其他文件使用 */
extern void uart_send_raw(uint8_t byte);